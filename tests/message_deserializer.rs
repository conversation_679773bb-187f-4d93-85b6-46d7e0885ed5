mod common;

use common::{
    assertions::*, compatibility_validators::validators::*, fixtures::*, mock_data::generators::*,
    reference_implementations::reference::*,
};

mod message_deserializer_tests {
    use super::*;

    #[test]
    fn test_message_deserializer_placeholder() {
        // TODO: Implement MessageVisitor, MessagePrefix tests
        // This is a Level 5 utils depending on all message types
        assert!(true);
    }
}
